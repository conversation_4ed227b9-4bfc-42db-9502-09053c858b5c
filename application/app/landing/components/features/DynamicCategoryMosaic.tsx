/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiSun,
	FiTrendingUp,
} from 'react-icons/fi';
import { colors } from '../../../colors';
import { POI_CATEGORIES_DATA } from '../../../shared/poi/constants';

// All available colors from design system (brand + supporting)
const ALL_COLORS = [
	colors.brand.blue,
	colors.brand.green,
	colors.brand.navy,
	colors.supporting.lightBlue,
	colors.supporting.mintGreen,
	colors.supporting.teal,
	colors.supporting.darkBlue,
	colors.supporting.purple,
	colors.supporting.softNavy,
];

// Helper functions from constants
const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

const getPOISubcategoriesWithCategory = () => {
	const allSubcategories: Array<{ subcategory: string; category: string }> = [];
	for (const category in POI_CATEGORIES_DATA) {
		const categoryData =
			POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
		for (const subcategory of categoryData.subcategories) {
			allSubcategories.push({ subcategory, category });
		}
	}
	return allSubcategories;
};

// Generates a gradient from a base color with 50% transparency
const getGradient = (color: string) => {
	const hexToRgb = (hex: string) => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		return result
			? {
					r: parseInt(result[1], 16),
					g: parseInt(result[2], 16),
					b: parseInt(result[3], 16),
			  }
			: null;
	};

	const baseRgb = hexToRgb(color);
	if (!baseRgb) return color;

	// Create a lighter version for gradient end
	const lighterRgb = {
		r: Math.min(255, baseRgb.r + 40),
		g: Math.min(255, baseRgb.g + 40),
		b: Math.min(255, baseRgb.b + 40),
	};

	const startColor = `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`;
	const endColor = `rgba(${lighterRgb.r}, ${lighterRgb.g}, ${lighterRgb.b}, 0.5)`;

	return `linear-gradient(45deg, ${startColor}, ${endColor})`;
};

// Shuffle function
const shuffleArray = <T,>(array: T[]): T[] => {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
};

interface CardData {
	subcategory: string;
	category: string;
	weight?: number;
	x?: number;
	y?: number;
	width?: number;
	height?: number;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CardData) => void;
}

// Treemap algorithm implementation
const generateTreemap = (
	data: CardData[],
	x: number,
	y: number,
	width: number,
	height: number
): CardData[] => {
	if (data.length === 0) return [];
	if (data.length === 1) {
		return [{ ...data[0], x, y, width, height }];
	}

	// Calculate total weight
	const totalWeight = data.reduce((sum, item) => sum + (item.weight || 1), 0);

	// Sort by weight (largest first)
	const sortedData = [...data].sort(
		(a, b) => (b.weight || 1) - (a.weight || 1)
	);

	// Split into two groups
	let leftWeight = 0;
	let splitIndex = 0;
	const targetWeight = totalWeight / 2;

	for (let i = 0; i < sortedData.length; i++) {
		leftWeight += sortedData[i].weight || 1;
		if (leftWeight >= targetWeight) {
			splitIndex = i + 1;
			break;
		}
	}

	const leftGroup = sortedData.slice(0, splitIndex);
	const rightGroup = sortedData.slice(splitIndex);

	// Decide split direction based on aspect ratio
	const isWiderThanTall = width > height;
	let leftResult: CardData[] = [];
	let rightResult: CardData[] = [];

	if (isWiderThanTall) {
		// Split vertically
		const leftWidth = (width * leftWeight) / totalWeight;
		const rightWidth = width - leftWidth;

		leftResult = generateTreemap(leftGroup, x, y, leftWidth, height);
		rightResult = generateTreemap(
			rightGroup,
			x + leftWidth,
			y,
			rightWidth,
			height
		);
	} else {
		// Split horizontally
		const leftHeight = (height * leftWeight) / totalWeight;
		const rightHeight = height - leftHeight;

		leftResult = generateTreemap(leftGroup, x, y, width, leftHeight);
		rightResult = generateTreemap(
			rightGroup,
			x,
			y + leftHeight,
			width,
			rightHeight
		);
	}

	return [...leftResult, ...rightResult];
};

// Get icon for subcategory - dynamic based on category type
const getCardIcon = (_subcategory: string, category: string) => {
	// Map categories to icon types
	const categoryIconMap: { [key: string]: JSX.Element } = {
		'Food & Drink': <FiCoffee className='w-full h-full text-white' />,
		'Cultural & Creative Experiences': (
			<FiBook className='w-full h-full text-white' />
		),
		'Sports & Fitness': <FiActivity className='w-full h-full text-white' />,
		Entertainment: <FiMusic className='w-full h-full text-white' />,
		'Shopping & Markets': (
			<FiShoppingBag className='w-full h-full text-white' />
		),
		'Outdoor & Nature': <FiSun className='w-full h-full text-white' />,
		'Wellness & Beauty': <FiHeart className='w-full h-full text-white' />,
		Transportation: <FiTrendingUp className='w-full h-full text-white' />,
	};

	return (
		categoryIconMap[category] || (
			<FiMapPin className='w-full h-full text-white' />
		)
	);
};

const CategoryCard: React.FC<{
	cardData: CardData;
	index: number;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
}> = ({ cardData, index, isHovered, onHover, onClick }) => {
	const { x = 0, y = 0, width = 0, height = 0 } = cardData;

	// Get color from expanded palette and create gradient with transparency
	const color = ALL_COLORS[index % ALL_COLORS.length];
	const gradient = getGradient(color);

	// Add spacing between cards
	const spacing = 4;
	const adjustedX = x + spacing;
	const adjustedY = y + spacing;
	const adjustedWidth = width - spacing * 2;
	const adjustedHeight = height - spacing * 2;

	return (
		<div
			style={{
				position: 'absolute',
				left: `${adjustedX}px`,
				top: `${adjustedY}px`,
				width: `${adjustedWidth}px`,
				height: `${adjustedHeight}px`,
				background: `${gradient}66`, // 40% transparency
				border: `3px solid ${color}CC`, // 80% border opacity
				borderRadius: '20px',
				boxSizing: 'border-box',
				overflow: 'hidden',
				color: 'white',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
				padding: adjustedWidth > 180 ? '24px' : '16px',
				textAlign: 'center',
				transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
				textShadow: '2px 2px 6px rgba(0,0,0,0.6)',
				transform: isHovered ? 'scale(1.08) translateY(-6px)' : 'scale(1)',
				zIndex: isHovered ? 10 : 1,
				boxShadow: isHovered
					? `0 25px 50px ${color}40, 0 12px 24px rgba(0,0,0,0.3)`
					: `0 6px 20px ${color}30, 0 2px 8px rgba(0,0,0,0.15)`,
				backdropFilter: 'blur(12px)',
				cursor: 'pointer',
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			{/* Icon with enhanced styling */}
			<div
				style={{
					marginBottom: adjustedWidth > 180 ? '20px' : '12px',
					fontSize:
						adjustedWidth > 250
							? '3em'
							: adjustedWidth > 180
							? '2.5em'
							: adjustedWidth > 120
							? '2em'
							: '1.5em',
					filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.4))',
					transform: isHovered ? 'scale(1.15) rotate(8deg)' : 'scale(1)',
					transition: 'all 0.4s ease',
				}}>
				{getCardIcon(cardData.subcategory, cardData.category)}
			</div>

			<h3
				style={{
					fontWeight: '900',
					fontSize:
						adjustedWidth > 250
							? '1.8em'
							: adjustedWidth > 180
							? '1.5em'
							: adjustedWidth > 120
							? '1.3em'
							: '1.1em',
					margin: 0,
					marginBottom: adjustedWidth > 180 ? '12px' : '8px',
					letterSpacing: '0.8px',
					lineHeight: '1.1',
					textTransform: 'uppercase',
				}}>
				{cardData.subcategory}
			</h3>

			<p
				style={{
					fontSize:
						adjustedWidth > 250
							? '1.2em'
							: adjustedWidth > 180
							? '1em'
							: adjustedWidth > 120
							? '0.9em'
							: '0.8em',
					opacity: 0.95,
					margin: 0,
					fontWeight: '600',
					letterSpacing: '0.4px',
					textTransform: 'capitalize',
				}}>
				{cardData.category}
			</p>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
	const [shuffledData, setShuffledData] = useState<
		Array<{ subcategory: string; category: string }>
	>([]);
	const [layoutCards, setLayoutCards] = useState<any[]>([]);

	// Get all categories and subcategories
	const allCategories = getPOICategories();
	const allSubcategories = getPOISubcategoriesWithCategory();

	// Initialize shuffled data on component mount (shuffle on refresh only)
	React.useEffect(() => {
		const shuffled = shuffleArray(allSubcategories);
		setShuffledData(shuffled);
	}, []); // Empty dependency array means this only runs on mount/refresh

	// Filter data based on selected category and subcategory
	const getFilteredData = () => {
		let filtered = shuffledData;

		if (selectedCategory !== 'all') {
			filtered = filtered.filter((item) => item.category === selectedCategory);
		}

		if (selectedSubcategory !== 'all') {
			filtered = filtered.filter(
				(item) => item.subcategory === selectedSubcategory
			);
		}

		return filtered.slice(0, 10); // Always show max 10 cards
	};

	// Generate layout only when filtered data changes (not on every render)
	React.useEffect(() => {
		const filteredData = getFilteredData();

		if (filteredData.length > 0) {
			// Add varied weights for much more dramatic size differences
			const subcategoriesWithWeights = filteredData.map((item, index) => {
				// Create very dramatic size variations with multiple factors
				const primaryFactor = Math.sin(index * 2.8) * 4; // Base variation
				const secondaryFactor = Math.cos(index * 1.7) * 3; // Secondary variation
				const tertiaryFactor = Math.sin(index * 0.9) * 2; // Fine-tuning variation

				// Combine factors for extreme size differences
				const combinedWeight =
					5 + primaryFactor + secondaryFactor + tertiaryFactor;

				return {
					...item,
					weight: Math.max(2, Math.min(12, combinedWeight)), // Range: 2-12 for huge differences
				};
			});

			// Sort by weight to encourage more varied groupings
			subcategoriesWithWeights.sort((a, b) => a.weight - b.weight);

			// Generate treemap layout
			const newLayoutCards = generateTreemap(
				subcategoriesWithWeights,
				0,
				0,
				1200,
				800
			);

			setLayoutCards(newLayoutCards);
		}
	}, [shuffledData, selectedCategory, selectedSubcategory]); // Only regenerate when these change

	// Get subcategories for selected category
	const getSubcategoriesForCategory = (category: string) => {
		if (category === 'all') return [];
		return allSubcategories
			.filter((item) => item.category === category)
			.map((item) => item.subcategory)
			.filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates
	};

	const availableSubcategories = getSubcategoriesForCategory(selectedCategory);

	return (
		<div className='space-y-8'>
			{/* Revolutionary Filter Design with Supporting Colors */}
			<div className='flex flex-col sm:flex-row gap-8 justify-center items-center mb-4'>
				{/* Category Filter */}
				<div className='flex flex-col items-center group'>
					<label
						className='text-sm font-bold mb-4 tracking-widest uppercase'
						style={{ color: colors.supporting.darkBlue }}>
						Category
					</label>
					<div className='relative'>
						<select
							value={selectedCategory}
							onChange={(e) => {
								setSelectedCategory(e.target.value);
								setSelectedSubcategory('all'); // Reset subcategory when category changes
							}}
							className='px-8 py-4 rounded-3xl border-3 transition-all duration-500 focus:ring-6 cursor-pointer shadow-2xl hover:shadow-3xl group-hover:scale-105'
							style={{
								borderColor: colors.supporting.teal,
								background: `linear-gradient(135deg, ${colors.supporting.lightBlue}40 0%, ${colors.supporting.mintGreen}30 50%, ${colors.neutral.cloudWhite} 100%)`,
								color: colors.supporting.darkBlue,
								minWidth: '260px',
								fontSize: '18px',
								fontWeight: '700',
								backdropFilter: 'blur(10px)',
								boxShadow: `0 10px 30px ${colors.supporting.teal}30, 0 4px 15px rgba(0,0,0,0.1)`,
							}}>
							<option value='all'>🌟 All Categories</option>
							{allCategories.slice(0, 3).map((category) => (
								<option
									key={category}
									value={category}>
									📍 {category}
								</option>
							))}
							{allCategories.length > 3 && (
								<optgroup label='🔍 More Categories'>
									{allCategories.slice(3).map((category) => (
										<option
											key={category}
											value={category}>
											📍 {category}
										</option>
									))}
								</optgroup>
							)}
						</select>
					</div>
				</div>

				{/* Subcategory Filter - Only show when category is selected */}
				{selectedCategory !== 'all' && (
					<div className='flex flex-col items-center group animate-fadeIn'>
						<label
							className='text-sm font-bold mb-4 tracking-widest uppercase'
							style={{ color: colors.supporting.purple }}>
							Subcategory
						</label>
						<div className='relative'>
							<select
								value={selectedSubcategory}
								onChange={(e) => setSelectedSubcategory(e.target.value)}
								className='px-8 py-4 rounded-3xl border-3 transition-all duration-500 focus:ring-6 cursor-pointer shadow-2xl hover:shadow-3xl group-hover:scale-105'
								style={{
									borderColor: colors.supporting.purple,
									background: `linear-gradient(135deg, ${colors.supporting.mintGreen}40 0%, ${colors.supporting.lightBlue}30 50%, ${colors.neutral.cloudWhite} 100%)`,
									color: colors.supporting.darkBlue,
									minWidth: '260px',
									fontSize: '18px',
									fontWeight: '700',
									backdropFilter: 'blur(10px)',
									boxShadow: `0 10px 30px ${colors.supporting.purple}30, 0 4px 15px rgba(0,0,0,0.1)`,
								}}>
								<option value='all'>✨ All Subcategories</option>
								{availableSubcategories.slice(0, 3).map((subcategory) => (
									<option
										key={subcategory}
										value={subcategory}>
										🎯 {subcategory}
									</option>
								))}
								{availableSubcategories.length > 3 && (
									<optgroup label='🔍 More Subcategories'>
										{availableSubcategories.slice(3).map((subcategory) => (
											<option
												key={subcategory}
												value={subcategory}>
												🎯 {subcategory}
											</option>
										))}
									</optgroup>
								)}
							</select>
						</div>
					</div>
				)}
			</div>

			{/* Transparent Card Container */}
			<div className='flex justify-center'>
				<div
					style={{
						width: '1200px',
						height: '800px',
						position: 'relative',
						boxSizing: 'border-box',
						overflow: 'hidden',
						background: 'transparent',
						margin: '0 auto',
					}}>
					{layoutCards.map((card, index) => (
						<CategoryCard
							key={`${card.category}-${card.subcategory}-${index}`}
							cardData={card}
							index={index}
							isHovered={
								hoveredCategory === `${card.category}-${card.subcategory}`
							}
							onHover={(hovered) =>
								setHoveredCategory(
									hovered ? `${card.category}-${card.subcategory}` : null
								)
							}
							onClick={() => onCategorySelect?.(card)}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
