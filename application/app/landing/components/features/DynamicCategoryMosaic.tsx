/** @format */

'use client';

import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiSun,
	FiTrendingUp,
} from 'react-icons/fi';
import { colors } from '../../../colors';
import { POI_CATEGORIES_DATA } from '../../../shared/poi/constants';

// Brand colors from your design system
const BRAND_COLORS = [colors.brand.blue, colors.brand.green, colors.brand.navy];

// Helper functions from constants
const getPOICategories = (): string[] => {
	return Object.keys(POI_CATEGORIES_DATA);
};

const getPOISubcategoriesWithCategory = () => {
	const allSubcategories: Array<{ subcategory: string; category: string }> = [];
	for (const category in POI_CATEGORIES_DATA) {
		const categoryData =
			POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];
		for (const subcategory of categoryData.subcategories) {
			allSubcategories.push({ subcategory, category });
		}
	}
	return allSubcategories;
};

// Generates a gradient from a base color with 50% transparency
const getGradient = (color: string) => {
	const hexToRgb = (hex: string) => {
		const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
		return result
			? {
					r: parseInt(result[1], 16),
					g: parseInt(result[2], 16),
					b: parseInt(result[3], 16),
			  }
			: null;
	};

	const baseRgb = hexToRgb(color);
	if (!baseRgb) return color;

	// Create a lighter version for gradient end
	const lighterRgb = {
		r: Math.min(255, baseRgb.r + 40),
		g: Math.min(255, baseRgb.g + 40),
		b: Math.min(255, baseRgb.b + 40),
	};

	const startColor = `rgba(${baseRgb.r}, ${baseRgb.g}, ${baseRgb.b}, 0.5)`;
	const endColor = `rgba(${lighterRgb.r}, ${lighterRgb.g}, ${lighterRgb.b}, 0.5)`;

	return `linear-gradient(45deg, ${startColor}, ${endColor})`;
};

// Shuffle function
const shuffleArray = <T,>(array: T[]): T[] => {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
};

interface CardData {
	subcategory: string;
	category: string;
	weight?: number;
	x?: number;
	y?: number;
	width?: number;
	height?: number;
}

interface DynamicCategoryMosaicProps {
	onCategorySelect?: (category: CardData) => void;
}

// Treemap algorithm implementation
const generateTreemap = (
	data: CardData[],
	x: number,
	y: number,
	width: number,
	height: number
): CardData[] => {
	if (data.length === 0) return [];
	if (data.length === 1) {
		return [{ ...data[0], x, y, width, height }];
	}

	// Calculate total weight
	const totalWeight = data.reduce((sum, item) => sum + (item.weight || 1), 0);

	// Sort by weight (largest first)
	const sortedData = [...data].sort(
		(a, b) => (b.weight || 1) - (a.weight || 1)
	);

	// Split into two groups
	let leftWeight = 0;
	let splitIndex = 0;
	const targetWeight = totalWeight / 2;

	for (let i = 0; i < sortedData.length; i++) {
		leftWeight += sortedData[i].weight || 1;
		if (leftWeight >= targetWeight) {
			splitIndex = i + 1;
			break;
		}
	}

	const leftGroup = sortedData.slice(0, splitIndex);
	const rightGroup = sortedData.slice(splitIndex);

	// Decide split direction based on aspect ratio
	const isWiderThanTall = width > height;
	let leftResult: CardData[] = [];
	let rightResult: CardData[] = [];

	if (isWiderThanTall) {
		// Split vertically
		const leftWidth = (width * leftWeight) / totalWeight;
		const rightWidth = width - leftWidth;

		leftResult = generateTreemap(leftGroup, x, y, leftWidth, height);
		rightResult = generateTreemap(
			rightGroup,
			x + leftWidth,
			y,
			rightWidth,
			height
		);
	} else {
		// Split horizontally
		const leftHeight = (height * leftWeight) / totalWeight;
		const rightHeight = height - leftHeight;

		leftResult = generateTreemap(leftGroup, x, y, width, leftHeight);
		rightResult = generateTreemap(
			rightGroup,
			x,
			y + leftHeight,
			width,
			rightHeight
		);
	}

	return [...leftResult, ...rightResult];
};

// Get icon for subcategory - dynamic based on category type
const getCardIcon = (_subcategory: string, category: string) => {
	// Map categories to icon types
	const categoryIconMap: { [key: string]: JSX.Element } = {
		'Food & Drink': <FiCoffee className='w-full h-full text-white' />,
		'Cultural & Creative Experiences': (
			<FiBook className='w-full h-full text-white' />
		),
		'Sports & Fitness': <FiActivity className='w-full h-full text-white' />,
		Entertainment: <FiMusic className='w-full h-full text-white' />,
		'Shopping & Markets': (
			<FiShoppingBag className='w-full h-full text-white' />
		),
		'Outdoor & Nature': <FiSun className='w-full h-full text-white' />,
		'Wellness & Beauty': <FiHeart className='w-full h-full text-white' />,
		Transportation: <FiTrendingUp className='w-full h-full text-white' />,
	};

	return (
		categoryIconMap[category] || (
			<FiMapPin className='w-full h-full text-white' />
		)
	);
};

const CategoryCard: React.FC<{
	cardData: CardData;
	index: number;
	isHovered: boolean;
	onHover: (hovered: boolean) => void;
	onClick: () => void;
}> = ({ cardData, index, isHovered, onHover, onClick }) => {
	const { x = 0, y = 0, width = 0, height = 0 } = cardData;

	// Get brand color and gradient - exactly like the example
	const color = BRAND_COLORS[index % BRAND_COLORS.length];
	const gradient = getGradient(color);

	return (
		<div
			style={{
				position: 'absolute',
				left: `${x}px`,
				top: `${y}px`,
				width: `${width}px`,
				height: `${height}px`,
				background: gradient,
				border: '2px solid #fff',
				borderRadius: '6px',
				boxSizing: 'border-box',
				overflow: 'hidden',
				color: 'white',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
				padding: '15px',
				textAlign: 'center',
				transition: 'transform 0.3s ease, box-shadow 0.3s ease',
				textShadow: '1px 1px 3px rgba(0,0,0,0.4)',
				transform: isHovered ? 'scale(1.02)' : 'scale(1)',
				zIndex: isHovered ? 10 : 1,
				boxShadow: isHovered ? '0 10px 20px rgba(0, 0, 0, 0.2)' : 'none',
				borderColor: isHovered ? '#fafafa' : '#fff',
				cursor: 'pointer',
			}}
			onMouseEnter={() => onHover(true)}
			onMouseLeave={() => onHover(false)}
			onClick={onClick}>
			{/* Icon */}
			<div style={{ marginBottom: '12px', fontSize: '2em' }}>
				{getCardIcon(cardData.subcategory, cardData.category)}
			</div>

			<h3
				style={{
					fontWeight: 'bold',
					fontSize: '1.5em',
					margin: 0,
				}}>
				{cardData.subcategory}
			</h3>

			<p
				style={{
					fontSize: '1em',
					opacity: 0.9,
					marginTop: '8px',
					margin: 0,
				}}>
				{cardData.category}
			</p>
		</div>
	);
};

const DynamicCategoryMosaic: React.FC<DynamicCategoryMosaicProps> = ({
	onCategorySelect,
}) => {
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
	const [shuffledData, setShuffledData] = useState<
		Array<{ subcategory: string; category: string }>
	>([]);
	const [layoutCards, setLayoutCards] = useState<any[]>([]);

	// Get all categories and subcategories
	const allCategories = getPOICategories();
	const allSubcategories = getPOISubcategoriesWithCategory();

	// Initialize shuffled data on component mount (shuffle on refresh only)
	React.useEffect(() => {
		const shuffled = shuffleArray(allSubcategories);
		setShuffledData(shuffled);
	}, []); // Empty dependency array means this only runs on mount/refresh

	// Filter data based on selected category and subcategory
	const getFilteredData = () => {
		let filtered = shuffledData;

		if (selectedCategory !== 'all') {
			filtered = filtered.filter((item) => item.category === selectedCategory);
		}

		if (selectedSubcategory !== 'all') {
			filtered = filtered.filter(
				(item) => item.subcategory === selectedSubcategory
			);
		}

		return filtered.slice(0, 10); // Always show max 10 cards
	};

	// Generate layout only when filtered data changes (not on every render)
	React.useEffect(() => {
		const filteredData = getFilteredData();

		if (filteredData.length > 0) {
			// Add random weights for more varied layout (but stable for same data)
			const subcategoriesWithWeights = filteredData.map((item, index) => ({
				...item,
				weight: Math.sin(index * 2.5) * 2 + 2.5, // Deterministic "random" weight based on index
			}));

			// Sort by weight to encourage more varied groupings
			subcategoriesWithWeights.sort((a, b) => a.weight - b.weight);

			// Generate treemap layout
			const newLayoutCards = generateTreemap(
				subcategoriesWithWeights,
				0,
				0,
				1200,
				800
			);

			setLayoutCards(newLayoutCards);
		}
	}, [shuffledData, selectedCategory, selectedSubcategory]); // Only regenerate when these change

	// Get subcategories for selected category
	const getSubcategoriesForCategory = (category: string) => {
		if (category === 'all') return [];
		return allSubcategories
			.filter((item) => item.category === category)
			.map((item) => item.subcategory)
			.filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates
	};

	const availableSubcategories = getSubcategoriesForCategory(selectedCategory);

	return (
		<div className='space-y-6'>
			{/* Filtering Controls */}
			<div className='flex flex-col sm:flex-row gap-4 justify-center items-center'>
				{/* Category Filter */}
				<div className='flex flex-col'>
					<label
						className='text-sm font-medium mb-2'
						style={{ color: colors.neutral.textBlack }}>
						Category
					</label>
					<div className='relative'>
						<select
							value={selectedCategory}
							onChange={(e) => {
								setSelectedCategory(e.target.value);
								setSelectedSubcategory('all'); // Reset subcategory when category changes
							}}
							className='px-4 py-2 rounded-lg border transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
							style={{
								borderColor: colors.ui.gray200,
								background: colors.neutral.cloudWhite,
								color: colors.neutral.textBlack,
								minWidth: '200px',
							}}>
							<option value='all'>All Categories</option>
							{allCategories.slice(0, 3).map((category) => (
								<option
									key={category}
									value={category}>
									{category}
								</option>
							))}
							{allCategories.length > 3 && (
								<optgroup label='More Categories'>
									{allCategories.slice(3).map((category) => (
										<option
											key={category}
											value={category}>
											{category}
										</option>
									))}
								</optgroup>
							)}
						</select>
					</div>
				</div>

				{/* Subcategory Filter - Only show when category is selected */}
				{selectedCategory !== 'all' && (
					<div className='flex flex-col'>
						<label
							className='text-sm font-medium mb-2'
							style={{ color: colors.neutral.textBlack }}>
							Subcategory
						</label>
						<div className='relative'>
							<select
								value={selectedSubcategory}
								onChange={(e) => setSelectedSubcategory(e.target.value)}
								className='px-4 py-2 rounded-lg border transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
								style={{
									borderColor: colors.ui.gray200,
									background: colors.neutral.cloudWhite,
									color: colors.neutral.textBlack,
									minWidth: '200px',
								}}>
								<option value='all'>All Subcategories</option>
								{availableSubcategories.slice(0, 3).map((subcategory) => (
									<option
										key={subcategory}
										value={subcategory}>
										{subcategory}
									</option>
								))}
								{availableSubcategories.length > 3 && (
									<optgroup label='More Subcategories'>
										{availableSubcategories.slice(3).map((subcategory) => (
											<option
												key={subcategory}
												value={subcategory}>
												{subcategory}
											</option>
										))}
									</optgroup>
								)}
							</select>
						</div>
					</div>
				)}
			</div>

			{/* Card Container */}
			<div className='flex justify-center'>
				<div
					style={{
						width: '1200px',
						height: '800px',
						border: '2px solid #ccc',
						position: 'relative',
						boxSizing: 'border-box',
						overflow: 'hidden',
						backgroundColor: '#fff',
						margin: '0 auto',
						boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
						borderRadius: '8px',
					}}>
					{layoutCards.map((card, index) => (
						<CategoryCard
							key={`${card.category}-${card.subcategory}-${index}`}
							cardData={card}
							index={index}
							isHovered={
								hoveredCategory === `${card.category}-${card.subcategory}`
							}
							onHover={(hovered) =>
								setHoveredCategory(
									hovered ? `${card.category}-${card.subcategory}` : null
								)
							}
							onClick={() => onCategorySelect?.(card)}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

export default DynamicCategoryMosaic;
