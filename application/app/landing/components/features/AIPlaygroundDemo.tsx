/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';
import {
	FiArrowRight,
	FiCheckCircle,
	FiMessageCircle,
	FiSearch,
	FiStar,
	FiZap,
} from 'react-icons/fi';

interface DemoScenario {
	id: string;
	userQuery: string;
	aiResponse: string;
	searchResults: LocationResult[];
	processingSteps: string[];
	conversationContext: string[];
	traditionalSteps: string[];
}

interface LocationResult {
	name: string;
	type: string;
	rating: number;
	distance: string;
	features: string[];
	image?: string;
}

const demoScenarios: DemoScenario[] = LANDING_PAGE_DATA.aiDemoScenarios.map(
	(scenario) => ({
		...scenario,
		conversationContext: [],
	})
);

const ProcessingStep: React.FC<{
	step: string;
	index: number;
	isActive: boolean;
	isCompleted: boolean;
}> = ({ step, index, isActive, isCompleted }) => (
	<div
		className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-500 ${
			isActive ? 'scale-105' : ''
		}`}
		style={{
			background: isActive
				? `linear-gradient(135deg, ${colors.brand.blue}20 0%, ${colors.brand.green}20 100%)`
				: isCompleted
				? colors.ui.green50
				: colors.ui.gray50,
			borderLeft: `4px solid ${
				isCompleted
					? colors.brand.green
					: isActive
					? colors.brand.blue
					: colors.ui.gray200
			}`,
		}}>
		<div
			className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 ${
				isActive ? 'animate-pulse' : ''
			}`}
			style={{
				background: isCompleted
					? colors.brand.green
					: isActive
					? colors.brand.blue
					: colors.ui.gray200,
			}}>
			{isCompleted ? (
				<FiCheckCircle className='w-4 h-4 text-white' />
			) : (
				<span className='text-xs font-bold text-white'>{index + 1}</span>
			)}
		</div>
		<span
			className={`text-sm ${isActive ? 'font-semibold' : ''}`}
			style={{
				color: isActive ? colors.neutral.textBlack : colors.neutral.slateGray,
			}}>
			{step}
		</span>
	</div>
);

const LocationCard: React.FC<{ location: LocationResult; index: number }> = ({
	location,
	index,
}) => (
	<div
		className='p-4 rounded-xl border transition-all duration-300 hover:scale-102'
		style={{
			background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
			borderColor: colors.ui.gray200,
			animationDelay: `${index * 100}ms`,
		}}>
		<div className='flex items-start justify-between mb-3'>
			<div>
				<h4
					className='font-semibold'
					style={{ color: colors.neutral.textBlack }}>
					{location.name}
				</h4>
				<p
					className='text-sm'
					style={{ color: colors.neutral.slateGray }}>
					{location.type} • {location.distance}
				</p>
			</div>
			<div className='flex items-center space-x-1'>
				<FiStar
					className='w-4 h-4'
					style={{ color: colors.brand.green }}
				/>
				<span
					className='text-sm font-medium'
					style={{ color: colors.neutral.textBlack }}>
					{location.rating}
				</span>
			</div>
		</div>

		<div className='flex flex-wrap gap-2'>
			{location.features.slice(0, 3).map((feature, idx) => (
				<span
					key={idx}
					className='px-2 py-1 rounded-full text-xs'
					style={{
						background: colors.ui.blue50,
						color: colors.neutral.slateGray,
					}}>
					{feature}
				</span>
			))}
		</div>
	</div>
);

const TraditionalSearchSteps: React.FC<{ steps: string[] }> = ({ steps }) => (
	<div className='space-y-3'>
		{steps.map((step, index) => (
			<div
				key={index}
				className='flex items-center space-x-3 p-3 rounded-lg'
				style={{ background: colors.ui.gray50 }}>
				<div
					className='w-6 h-6 rounded-full flex items-center justify-center'
					style={{ background: colors.ui.gray200 }}>
					<span className='text-xs font-bold text-gray-600'>{index + 1}</span>
				</div>
				<span
					className='text-sm'
					style={{ color: colors.neutral.slateGray }}>
					{step}
				</span>
			</div>
		))}
	</div>
);

const AIPlaygroundDemo: React.FC<{ onGetStarted?: () => void }> = ({
	onGetStarted,
}) => {
	const [activeScenario, setActiveScenario] = useState(0);
	const [activeStep, setActiveStep] = useState(0);
	const [isPlaying, setIsPlaying] = useState(false);
	const [showComparison, setShowComparison] = useState(false);

	const currentScenario = demoScenarios[activeScenario];

	useEffect(() => {
		if (isPlaying) {
			const timer = setInterval(() => {
				setActiveStep((prev) => {
					if (prev >= currentScenario.processingSteps.length - 1) {
						setIsPlaying(false);
						setShowComparison(true);
						return prev;
					}
					return prev + 1;
				});
			}, 1000);

			return () => clearInterval(timer);
		}
	}, [isPlaying, currentScenario.processingSteps.length]);

	const handlePlayDemo = () => {
		setActiveStep(0);
		setShowComparison(false);
		setIsPlaying(true);
	};

	const resetDemo = () => {
		setActiveStep(0);
		setIsPlaying(false);
		setShowComparison(false);
	};

	return (
		<div className='w-full'>
			{/* Enhanced Animations */}
			<style jsx>{`
				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(30px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}
				@keyframes pulse-glow {
					0%,
					100% {
						box-shadow: 0 0 20px rgba(51, 194, 255, 0.3);
					}
					50% {
						box-shadow: 0 0 30px rgba(51, 194, 255, 0.5);
					}
				}
				.animate-fade-in {
					animation: fadeInUp 0.8s ease-out forwards;
				}
				.animate-pulse-glow {
					animation: pulse-glow 2s ease-in-out infinite;
				}
			`}</style>

			{/* Demo Controls with Enhanced Styling */}
			<div className='flex justify-center mb-8'>
				<div
					className='flex items-center space-x-4 p-4 rounded-2xl backdrop-blur-sm border border-white/20'
					style={{
						background: `linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)`,
						boxShadow: '0 8px 32px rgba(51, 194, 255, 0.1)',
					}}>
					<button
						onClick={handlePlayDemo}
						disabled={isPlaying}
						className={`flex items-center space-x-2 px-8 py-4 rounded-xl transition-all duration-500 ${
							isPlaying
								? 'opacity-50 cursor-not-allowed'
								: 'hover:scale-105 hover:shadow-lg'
						}`}
						style={{
							background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							color: 'white',
							boxShadow: isPlaying
								? 'none'
								: '0 4px 15px rgba(51, 194, 255, 0.3)',
						}}>
						<FiZap className={`w-5 h-5 ${isPlaying ? 'animate-spin' : ''}`} />
						<span className='font-semibold'>
							{isPlaying ? 'Processing...' : 'Watch AI in Action'}
						</span>
					</button>

					<button
						onClick={resetDemo}
						className='px-6 py-3 rounded-xl border transition-all duration-300 hover:scale-105 hover:shadow-md'
						style={{
							borderColor: colors.ui.gray200,
							background: colors.neutral.cloudWhite,
							color: colors.neutral.textBlack,
						}}>
						Reset Demo
					</button>
				</div>
			</div>

			{/* Enhanced Scenario Selector */}
			<div className='flex justify-center mb-8'>
				<div
					className='flex space-x-4 p-2 rounded-2xl backdrop-blur-sm border border-white/10'
					style={{
						background: `linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)`,
					}}>
					{demoScenarios.map((scenario, index) => (
						<button
							key={scenario.id}
							onClick={() => {
								setActiveScenario(index);
								setActiveStep(0);
								setIsPlaying(false);
								setShowComparison(false);
							}}
							className={`px-8 py-4 rounded-xl transition-all duration-500 font-semibold ${
								activeScenario === index
									? 'scale-105 shadow-lg'
									: 'hover:scale-102 hover:shadow-md'
							}`}
							style={{
								background:
									activeScenario === index
										? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
										: `linear-gradient(135deg, ${colors.ui.gray100} 0%, ${colors.neutral.cloudWhite} 100%)`,
								color:
									activeScenario === index ? 'white' : colors.neutral.textBlack,
								boxShadow:
									activeScenario === index
										? '0 8px 25px rgba(51, 194, 255, 0.3)'
										: '0 2px 10px rgba(0, 0, 0, 0.1)',
								border:
									activeScenario === index
										? 'none'
										: `1px solid ${colors.ui.gray200}`,
							}}>
							{scenario.id === 'cozy-cafe' ? 'Work Cafe' : 'Romantic Dinner'}
						</button>
					))}
				</div>
			</div>

			{/* Enhanced Main Demo Area */}
			<div className='grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8'>
				{/* AI Processing Side with Enhanced Styling */}
				<div
					className='p-8 rounded-3xl border backdrop-blur-sm transition-all duration-500 hover:shadow-xl'
					style={{
						background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
						borderColor: colors.ui.gray200,
						boxShadow: '0 10px 30px rgba(51, 194, 255, 0.1)',
					}}>
					<div className='flex items-center space-x-3 mb-6'>
						<div
							className='w-12 h-12 rounded-xl flex items-center justify-center'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							<FiMessageCircle className='w-6 h-6 text-white' />
						</div>
						<div>
							<h3
								className='text-xl font-bold'
								style={{ color: colors.neutral.textBlack }}>
								AI-Powered Search
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								Context-aware processing
							</p>
						</div>
					</div>

					{/* User Query */}
					<div
						className='p-4 rounded-xl mb-6'
						style={{ background: colors.ui.blue50 }}>
						<p
							className='text-sm font-medium mb-2'
							style={{ color: colors.neutral.slateGray }}>
							User Query:
						</p>
						<p
							className='font-semibold'
							style={{ color: colors.neutral.textBlack }}>
							"{currentScenario.userQuery}"
						</p>
					</div>

					{/* Processing Steps */}
					<div className='space-y-3'>
						{currentScenario.processingSteps.map((step, index) => (
							<ProcessingStep
								key={index}
								step={step}
								index={index}
								isActive={isPlaying && index === activeStep}
								isCompleted={index < activeStep || showComparison}
							/>
						))}
					</div>
				</div>

				{/* Traditional Search Side with Enhanced Styling */}
				<div
					className='p-8 rounded-3xl border backdrop-blur-sm transition-all duration-500 hover:shadow-lg'
					style={{
						background: `linear-gradient(135deg, ${colors.ui.gray50} 0%, ${colors.neutral.cloudWhite} 100%)`,
						borderColor: colors.ui.gray200,
						boxShadow: '0 10px 30px rgba(0, 0, 0, 0.05)',
					}}>
					<div className='flex items-center space-x-3 mb-6'>
						<div
							className='w-12 h-12 rounded-xl flex items-center justify-center'
							style={{ background: colors.ui.gray200 }}>
							<FiSearch className='w-6 h-6 text-gray-600' />
						</div>
						<div>
							<h3
								className='text-xl font-bold'
								style={{ color: colors.neutral.textBlack }}>
								Traditional Search
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								Keyword-based filtering
							</p>
						</div>
					</div>

					{/* Traditional Steps */}
					<TraditionalSearchSteps steps={currentScenario.traditionalSteps} />

					{/* Comparison Note */}
					<div
						className='mt-6 p-4 rounded-xl'
						style={{ background: colors.ui.gray50 }}>
						<p
							className='text-sm'
							style={{ color: colors.neutral.slateGray }}>
							Traditional search requires multiple manual steps and filters to
							achieve similar results.
						</p>
					</div>
				</div>
			</div>

			{/* Enhanced Results Comparison */}
			{showComparison && (
				<div
					className='p-8 rounded-3xl border backdrop-blur-sm transition-all duration-700 animate-fade-in'
					style={{
						background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.ui.blue50} 100%)`,
						borderColor: colors.ui.gray200,
						boxShadow: '0 20px 40px rgba(128, 237, 153, 0.15)',
						animation: 'fadeInUp 0.8s ease-out forwards',
					}}>
					<div className='text-center mb-6'>
						<h3
							className='text-2xl font-bold mb-2'
							style={{ color: colors.neutral.textBlack }}>
							AI Results
						</h3>
						<p style={{ color: colors.neutral.slateGray }}>
							Personalized recommendations based on context understanding
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-6'>
						{currentScenario.searchResults.map((location, index) => (
							<LocationCard
								key={index}
								location={location}
								index={index}
							/>
						))}
					</div>

					{/* CTA */}
					{onGetStarted && (
						<div className='text-center'>
							<button
								onClick={onGetStarted}
								className='inline-flex items-center space-x-2 px-8 py-4 rounded-xl transition-all duration-300 hover:scale-105'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
									color: 'white',
								}}>
								<span>Try It Yourself</span>
								<FiArrowRight className='w-5 h-5' />
							</button>
						</div>
					)}
				</div>
			)}
		</div>
	);
};

export default AIPlaygroundDemo;
